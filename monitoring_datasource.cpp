#include "monitoring_datasource.h"
#include <QVariantMap>
#include <QtCharts/QXYSeries>
#include <QtCharts/QLineSeries>
#include <QCoreApplication>
#include <QtCharts/QAbstractSeries>
#include <QDateTime>
#include <QTimer>
#include <QMutexLocker>
#include <QThreadPool>
#include <QRunnable>
#include <QMetaObject>
#include <thread>
#include <chrono>

// Qt 5.x需要使用QtCharts命名空间
QT_CHARTS_USE_NAMESPACE

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#endif

// BoilerSwitchTask类已移除，因为使用简化的锅炉切换逻辑

// 条件编译：启用硬件数据采集
#define ENABLE_HARDWARE_DATA
#ifdef ENABLE_HARDWARE_DATA
#include "smoke_analyzer_comm.h"
#include "boiler.h"
#include "config_manager.h"
#endif

MonitoringDataSource::MonitoringDataSource(QObject *parent)
    : QObject(parent)
    , m_timer(new QTimer(this))
    , m_isBackflowActive(false)
    , m_isDataUpdateSuspended(false)
    , m_backflowDelayTimer(new QTimer(this))
    , m_backflowDelayTime(60)
    , m_suspendedO2Value("0.00%")
    , m_suspendedCOValue("0ppm")
    , m_isRunning(false)
    , m_isDataConnected(false)
    , m_connectionStatus("未连接串口数据采集设备")
    , m_dataCount(0)
    , m_currentTemperature("0.0℃")
    , m_currentVoltage("0.0kPa")
    , m_currentCurrent("0.000A")
    , m_dataStartTimeSet(false)
{
    connect(m_timer, &QTimer::timeout, this, &MonitoringDataSource::updateData);
    // 不设置初始间隔，等待从配置文件读取

    // 初始化反吹反馈延迟定时器
    m_backflowDelayTimer->setSingleShot(true);
    connect(m_backflowDelayTimer, &QTimer::timeout, this, &MonitoringDataSource::resumeO2COUpdates);

    // 加载锅炉列表
    loadBoilerList();

    // 设置初始定时器间隔（从配置文件读取）
    updateTimerInterval();
}

void MonitoringDataSource::setIsRunning(bool running)
{
    if (m_isRunning != running) {
        m_isRunning = running;
        emit isRunningChanged();

        if (running) {
            startMonitoring();
        } else {
            stopMonitoring();
        }
    }
}

void MonitoringDataSource::setCurrentBoiler(const QString &boiler)
{
    if (m_currentBoiler != boiler) {
        debug_printf("切换烟气分析仪: 从 '%s' 到 '%s'\n",
                    m_currentBoiler.toStdString().c_str(), boiler.toStdString().c_str());

        // 简化的烟气分析仪切换逻辑，因为每个设备都有独立线程在采集数据
        m_currentBoiler = boiler;

        // 更新定时器间隔以匹配新锅炉的采集间隔
        updateTimerInterval();

        // 立即发射信号，让UI知道设备已经改变
        emit currentBoilerChanged();

        // 清空历史数据，准备显示新设备的数据
        clearAllData();  // 使用统一的清空方法
        m_smokeTableData.clear();
        m_dataCount = 0;

        // 重置相对时间轴的开始时间
        m_dataStartTimeSet = false;

        // 重置连接状态，让下次updateData时重新检测
        m_isDataConnected = false;
        m_connectionStatus = "正在检测新烟气分析仪的串口连接...";
        emit dataConnectionChanged();

        // 发射数据变化信号
        emit smokeDataChanged();
        emit smokeTableDataChanged();
        emit chartDataUpdated();

        // 如果监控正在运行，立即检测新设备的连接状态
        if (m_isRunning) {
            updateData();
        }
    }
}

void MonitoringDataSource::startMonitoring()
{
    if (!m_timer->isActive()) {
        m_timer->start();
        m_isRunning = true;
        emit isRunningChanged();

        // 初始化连接状态检查
        m_isDataConnected = false;
        m_connectionStatus = "正在检测串口数据采集设备...";
        emit dataConnectionChanged();

        // 立即进行一次数据更新和连接状态检测，不等待定时器
        debug_printf("UI监控启动: 立即进行首次连接状态检测\n");
        updateData();

        // 基于配置的动态延迟更新数据，确保图表能正常显示
        int collectionInterval = getCurrentCollectionInterval();
        int firstDelay = collectionInterval * 1000;      // 第一次延迟：1个采集周期
        int secondDelay = collectionInterval * 1500;     // 第二次延迟：1.5个采集周期

        debug_printf("UI监控: 使用动态延迟 - 第一次=%d毫秒, 第二次=%d毫秒\n",
                    firstDelay, secondDelay);

        QTimer::singleShot(firstDelay, this, [this, collectionInterval]() {
            debug_printf("UI监控: 延迟%d秒后再次更新数据\n", collectionInterval);
            updateData();
        });

        QTimer::singleShot(secondDelay, this, [this, collectionInterval]() {
            debug_printf("UI监控: 延迟%.1f秒后第三次更新数据\n", collectionInterval * 1.5);
            updateData();
        });
    }
}



void MonitoringDataSource::stopMonitoring()
{
    if (m_timer->isActive()) {
        m_timer->stop();
        m_isRunning = false;
        emit isRunningChanged();

        // 停止监控时重置连接状态
        m_isDataConnected = false;
        m_connectionStatus = "数据监控已停止";
        emit dataConnectionChanged();
    }
}

int MonitoringDataSource::getCurrentCollectionInterval() const
{
    // 获取当前锅炉的采集间隔
    int interval = 15;  // 默认值

    if (!m_currentBoiler.isEmpty()) {
        extern ConfigManager* g_config_manager;
        if (g_config_manager) {
            interval = g_config_manager->get<int>(m_currentBoiler.toStdString(), "CollectionInterval", 15);
        }
    }

    return interval;
}

int MonitoringDataSource::getBackflowDelayTime() const
{
    // 从配置文件读取反吹反馈延迟时间
    int delayTime = 60;  // 默认60秒

    if (!m_currentBoiler.isEmpty()) {
        extern ConfigManager* g_config_manager;
        if (g_config_manager) {
            delayTime = g_config_manager->get<int>(m_currentBoiler.toStdString(), "BackflowDelayTime", 60);
        }
    }

    return delayTime;
}

void MonitoringDataSource::checkBackflowStatus(int switch1)
{
    bool currentBackflowActive = (switch1 == 1);  // 修正：1表示反吹运行，0表示停止

    // 检查反吹反馈状态是否发生变化
    if (currentBackflowActive != m_isBackflowActive) {
        m_isBackflowActive = currentBackflowActive;

        if (m_isBackflowActive) {
            // 反吹反馈开始运行，暂停氧气和一氧化碳数值更新
            suspendO2COUpdates();
            debug_printf("监控系统: 检测到反吹反馈开始运行，暂停氧气和一氧化碳数值更新\n");
        } else {
            // 反吹反馈停止，启动延迟恢复定时器
            m_backflowDelayTime = getBackflowDelayTime();
            m_backflowDelayTimer->start(m_backflowDelayTime * 1000);  // 转换为毫秒
            debug_printf("监控系统: 检测到反吹反馈停止，将在%d秒后恢复氧气和一氧化碳数值更新\n", m_backflowDelayTime);
        }
    }
}

void MonitoringDataSource::suspendO2COUpdates()
{
    if (!m_isDataUpdateSuspended) {
        m_isDataUpdateSuspended = true;

        // 停止延迟恢复定时器（如果正在运行）
        if (m_backflowDelayTimer->isActive()) {
            m_backflowDelayTimer->stop();
        }

        // 保存当前的O2和CO数值作为暂停前的最后数值
        // 注意：这里的数值会在checkBackflowStatus调用之前通过updateSmokeData获取到
        debug_printf("监控系统: 氧气和一氧化碳数值更新已暂停，保存的暂停前数值 - O2: %s, CO: %s\n",
                    m_suspendedO2Value.toStdString().c_str(), m_suspendedCOValue.toStdString().c_str());
    }
}

void MonitoringDataSource::resumeO2COUpdates()
{
    if (m_isDataUpdateSuspended) {
        m_isDataUpdateSuspended = false;
        debug_printf("监控系统: 延迟%d秒后，氧气和一氧化碳数值更新已恢复\n", m_backflowDelayTime);

        // 立即触发一次数据更新，以显示最新的氧气和一氧化碳数值
        updateData();
    }
}

void MonitoringDataSource::clearData()
{
    // 清空所有数据
    clearAllData();

    m_smokeTableData.clear();
    m_dataCount = 0;

    // 重置相对时间轴的开始时间
    m_dataStartTimeSet = false;

    emit smokeDataChanged();
    emit smokeTableDataChanged();
    emit chartDataUpdated();
}

void MonitoringDataSource::clearAllData()
{
    // 清空滑动窗口数据
    m_smokeO2Data.clear();
    m_smokeCOData.clear();
    m_smokeSwitch1Data.clear();

    // 重置窗口管理变量
    m_windowStartTime = QDateTime();
    m_totalDataPoints = 0;
    m_lastCleanupCount = 0;

    debug_printf("渐进式数据管理: 所有数据已清空\n");
}

void MonitoringDataSource::addDataPoint(double o2, double co, int switch1, double relativeTimeHours, double relativeTimeMinutes)
{
    // 创建数据点
    QVariantMap o2Point, coPoint, switch1Point;

    o2Point["x"] = relativeTimeHours;
    o2Point["x_minutes"] = relativeTimeMinutes;
    o2Point["y"] = o2;

    coPoint["x"] = relativeTimeHours;
    coPoint["x_minutes"] = relativeTimeMinutes;
    coPoint["y"] = co;

    switch1Point["x"] = relativeTimeHours;
    switch1Point["x_minutes"] = relativeTimeMinutes;
    switch1Point["y"] = switch1;

    // 添加到滑动窗口
    m_smokeO2Data.append(o2Point);
    m_smokeCOData.append(coPoint);
    m_smokeSwitch1Data.append(switch1Point);

    m_totalDataPoints++;

    debug_printf("渐进式数据管理: 添加数据点 - 时间=%.3f小时, O2=%.2f%%, CO=%.0fppm, 总点数=%d\n",
                relativeTimeHours, o2, co, m_totalDataPoints);

    // 维护数据窗口大小
    maintainDataWindow();

    // 定期执行内存清理
    if (m_totalDataPoints - m_lastCleanupCount >= CLEANUP_THRESHOLD) {
        performMemoryCleanup();
        m_lastCleanupCount = m_totalDataPoints;
    }
}

void MonitoringDataSource::maintainDataWindow()
{
    // 维护滑动窗口：保留最近25小时的数据（支持24小时视图）
    if (m_smokeO2Data.isEmpty()) {
        return;
    }

    // 获取当前时间和窗口开始时间
    QDateTime currentTime = QDateTime::currentDateTime();
    if (m_windowStartTime.isNull()) {
        m_windowStartTime = currentTime;
    }

    // 计算数据窗口的时间范围（25小时）
    QDateTime windowCutoff = currentTime.addSecs(-DATA_WINDOW_HOURS * 3600);

    // 移除超出时间窗口的旧数据
    int removedCount = 0;
    while (!m_smokeO2Data.isEmpty()) {
        QVariantMap firstPoint = m_smokeO2Data.first().toMap();
        double firstPointTimeHours = firstPoint["x"].toDouble();

        // 计算绝对时间
        QDateTime pointTime = m_dataStartTime.addSecs(firstPointTimeHours * 3600);

        if (pointTime < windowCutoff) {
            m_smokeO2Data.removeFirst();
            m_smokeCOData.removeFirst();
            m_smokeSwitch1Data.removeFirst();
            removedCount++;
        } else {
            break;
        }
    }

    // 限制最大内存点数（硬限制）
    while (m_smokeO2Data.size() > MAX_MEMORY_POINTS) {
        m_smokeO2Data.removeFirst();
        m_smokeCOData.removeFirst();
        m_smokeSwitch1Data.removeFirst();
        removedCount++;
    }

    if (removedCount > 0) {
        debug_printf("渐进式数据管理: 维护数据窗口，移除了%d个旧数据点，当前点数=%d\n",
                    removedCount, m_smokeO2Data.size());
    }
}

void MonitoringDataSource::performMemoryCleanup()
{
    // 执行内存清理和优化
    debug_printf("渐进式数据管理: 执行内存清理，当前数据点数=%d\n", m_smokeO2Data.size());

    // 强制维护数据窗口
    maintainDataWindow();

    // 处理待处理的事件，释放内存
    QCoreApplication::processEvents();

    // 如果数据点数仍然过多，进行额外的清理
    if (m_smokeO2Data.size() > MAX_MEMORY_POINTS * 0.8) {
        // 保留最新的80%数据
        int keepCount = static_cast<int>(MAX_MEMORY_POINTS * 0.8);
        int removeCount = m_smokeO2Data.size() - keepCount;

        if (removeCount > 0) {
            for (int i = 0; i < removeCount; ++i) {
                m_smokeO2Data.removeFirst();
                m_smokeCOData.removeFirst();
                m_smokeSwitch1Data.removeFirst();
            }
            debug_printf("渐进式数据管理: 额外清理了%d个数据点\n", removeCount);
        }
    }
}

void MonitoringDataSource::updateData()
{
    static QDateTime lastUpdateTime = QDateTime::currentDateTime();
    QDateTime currentTime = QDateTime::currentDateTime();
    qint64 timeDiff = lastUpdateTime.msecsTo(currentTime);

    debug_printf("UI数据更新调用 - 距离上次更新: %lld毫秒, 定时器间隔: %d毫秒\n",
                timeDiff, m_timer->interval());

    updateSmokeData();
    m_dataCount++;

    lastUpdateTime = currentTime;
}

void MonitoringDataSource::updateSmokeData()
{
    // 检查是否有选择的烟气分析仪
    if (m_currentBoiler.isEmpty()) {
        return;
    }

    // 从硬件获取真实烟气数据
    float o2 = 0.0f, co = 0.0f;
    float current = 0.0f, voltage = 0.0f, temperature = 0.0f;
    int switch1 = 1;  // 开关量信号，默认关闭状态
    bool hardwareConnected = false;

    // 检查硬件连接状态
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;
    std::string deviceName = m_currentBoiler.toStdString();

    auto it = boiler_map.find(deviceName);
    if (it != boiler_map.end() && it->second != nullptr && it->second->fd >= 0) {
        hardwareConnected = true;

        // 立即获取数据，不等待data_ready标志
        // 这样可以让UI更快地显示已采集到的参数值
        try {
            get_realtime_data(deviceName, &co, &o2, &current, &voltage, &temperature, &switch1);

            // 在检查反吹状态之前，先保存当前的O2和CO数值（作为潜在的暂停前数值）
            if (!m_isDataUpdateSuspended) {
                // 只在数据更新未暂停时更新暂停前的数值
                m_suspendedO2Value = QString::number(o2, 'f', 2) + "%";
                m_suspendedCOValue = QString::number(co, 'f', 0) + "ppm";
            }

            // 检查反吹反馈状态并处理氧气和一氧化碳数值更新控制
            checkBackflowStatus(switch1);

            // 如果数据更新被暂停，记录日志
            if (m_isDataUpdateSuspended) {
                debug_printf("监控系统: 反吹反馈运行中，氧气和一氧化碳数值保持暂停状态 - O2: %s, CO: %s\n",
                            m_suspendedO2Value.toStdString().c_str(), m_suspendedCOValue.toStdString().c_str());
            }

            // 如果是首次获取到有效数据，记录日志
            if (!it->second->data_ready && (o2 > 0 || co > 0)) {
                debug_printf("UI开始显示烟气分析仪 %s 的部分数据\n", deviceName.c_str());
            }
        } catch (...) {
            // 如果数据采集函数出现异常，设置为无效数据
            debug_printf("烟气数据获取异常: %s\n", deviceName.c_str());
            o2 = co = current = voltage = temperature = 0.0f;
            switch1 = 0;  // 异常时设置为停止状态
        }
    }
#endif

    // 更新连接状态
    if (hardwareConnected) {
        // 硬件连接时直接使用真实数据，不做额外的有效性判断
        // 更新连接状态
        if (!m_isDataConnected) {
            m_isDataConnected = true;
            m_connectionStatus = "烟气分析仪已连接";
            debug_printf("UI连接状态: 烟气分析仪已连接\n");
            emit dataConnectionChanged();
        }


        // 获取当前时间
        QDateTime now = QDateTime::currentDateTime();

        // 如果是第一次采集数据，设置开始时间
        if (!m_dataStartTimeSet) {
            m_dataStartTime = now;
            m_dataStartTimeSet = true;
        }

        // 获取当前锅炉的采集间隔配置
        int collectionIntervalSeconds = 15; // 默认15秒
#ifdef ENABLE_HARDWARE_DATA
        extern std::unordered_map<std::string, Boiler*> boiler_map;
        extern ConfigManager* g_config_manager;

        std::string boilerName = m_currentBoiler.toStdString();
        auto it = boiler_map.find(boilerName);
        if (it != boiler_map.end() && it->second != nullptr && it->second->is_initialized) {
            collectionIntervalSeconds = it->second->collection_interval;
        } else if (g_config_manager && g_config_manager->exists(boilerName, "CollectionInterval")) {
            collectionIntervalSeconds = g_config_manager->get<int>(boilerName, "CollectionInterval", 15);
        }
#endif

        // 计算基于配置采集间隔的相对时间，而不是基于实际时间流逝
        // 这样确保图表上的数据点位置严格按照配置的采集间隔排列
        double relativeTimeHours = (m_dataCount * collectionIntervalSeconds) / (60.0 * 60.0);  // 转换为小时
        double relativeTimeMinutes = (m_dataCount * collectionIntervalSeconds) / 60.0;  // 转换为分钟

        // 更新图表数据
        // 氧气和一氧化碳数据：在反吹期间使用暂停前的数值继续添加数据点
        if (!m_isDataUpdateSuspended) {
            // 正常期间：使用实时数值
            addDataPoint(o2, co, switch1, relativeTimeHours, relativeTimeMinutes);
            debug_printf("数据添加: 正常模式添加数据 - 时间=%.3f小时, O2=%.2f%%, CO=%.0fppm\n",
                        relativeTimeHours, o2, co);
        } else {
            // 反吹期间：使用暂停前的数值继续添加数据点，保持曲线连续性
            // 从暂停的数值字符串中提取数字部分
            QString suspendedO2Str = m_suspendedO2Value;
            QString suspendedCOStr = m_suspendedCOValue;
            suspendedO2Str.remove("%");
            suspendedCOStr.remove("ppm");

            double suspendedO2 = suspendedO2Str.toDouble();
            double suspendedCO = suspendedCOStr.toDouble();

            addDataPoint(suspendedO2, suspendedCO, switch1, relativeTimeHours, relativeTimeMinutes);
            debug_printf("数据添加: 反吹模式添加数据 - 时间=%.3f小时, O2=%.2f%%(暂停前), CO=%.0fppm(暂停前)\n",
                        relativeTimeHours, suspendedO2, suspendedCO);
        }

        // 更新当前数据值
        m_currentTemperature = QString::number(temperature, 'f', 1) + "℃";
        m_currentVoltage = QString::number(voltage, 'f', 4) + "kPa";  // 压力值保留4位小数
        m_currentCurrent = QString::number(current, 'f', 3) + "A";

        // 添加表格数据（始终添加，但在反吹期间使用暂停前的O2和CO数值）
        if (m_isDataUpdateSuspended) {
            // 反吹期间：使用暂停前的O2和CO数值，其他参数使用实时数值
            // 从暂停的数值字符串中提取数字部分
            QString suspendedO2Str = m_suspendedO2Value;
            QString suspendedCOStr = m_suspendedCOValue;
            suspendedO2Str.remove("%");
            suspendedCOStr.remove("ppm");

            double suspendedO2 = suspendedO2Str.toDouble();
            double suspendedCO = suspendedCOStr.toDouble();

            addSmokeTableRow(suspendedO2, suspendedCO, temperature, voltage, current, switch1);
            debug_printf("监控系统: 反吹反馈运行中，表格数据使用暂停前的O2=%.2f, CO=%.0f\n", suspendedO2, suspendedCO);
        } else {
            // 正常期间：使用实时数值
            addSmokeTableRow(o2, co, temperature, voltage, current, switch1);
        }
    } else {
        // 硬件未连接
        if (m_isDataConnected) {
            m_isDataConnected = false;
            m_connectionStatus = "烟气分析仪未连接";
            debug_printf("UI连接状态: 烟气分析仪未连接\n");
            emit dataConnectionChanged();
        }

        // 硬件未连接时设置默认值
        m_currentTemperature = "0.0℃";
        m_currentVoltage = "0.0000kPa";
        m_currentCurrent = "0.000A";
        // 不添加任何数据到图表和表格
    }

    emit smokeDataChanged();
    emit currentDataChanged();

    // 只在有数据且硬件连接时发射图表更新信号，避免重复发射
    if (m_smokeO2Data.size() > 0 && hardwareConnected) {
        emit chartDataUpdated();
    }

    // 调试信息：输出当前状态
    debug_printf("监控系统状态: 硬件连接=%s, 数据连接=%s, 表格数据行数=%d, 图表数据点数=%d\n",
                hardwareConnected ? "是" : "否",
                m_isDataConnected ? "是" : "否",
                m_smokeTableData.size(),
                m_smokeO2Data.size());
}

void MonitoringDataSource::loadBoilerList()
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;

    m_boilerList.clear();

    // 从全局设备映射中获取烟气分析仪列表
    for (const auto& pair : boiler_map) {
        m_boilerList.append(QString::fromStdString(pair.first));
    }

    // 如果有烟气分析仪，设置第一个为默认选择
    if (!m_boilerList.isEmpty() && m_currentBoiler.isEmpty()) {
        m_currentBoiler = m_boilerList.first();
        debug_printf("设置默认烟气分析仪: '%s'\n", m_currentBoiler.toStdString().c_str());

        // 如果监控正在运行，立即检测默认设备的连接状态
        if (m_isRunning) {
            debug_printf("默认设备设置: 立即检测连接状态\n");
            updateData();
        }
    }

    emit boilerListChanged();
    emit currentBoilerChanged();
#else
    // 如果硬件数据采集被禁用，提供默认烟气分析仪列表
    m_boilerList << "SmokeAnalyzer1" << "SmokeAnalyzer2";
    if (m_currentBoiler.isEmpty()) {
        m_currentBoiler = "SmokeAnalyzer1";
    }
    emit boilerListChanged();
    emit currentBoilerChanged();
#endif
}



void MonitoringDataSource::addSmokeTableRow(double o2, double co, double temperature, double voltage, double current, int switch1)
{
    QVariantMap row;
    row["time"] = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
    row["o2"] = QString::number(o2, 'f', 3);
    row["co"] = QString::number(co, 'f', 0);
    row["temperature"] = QString::number(temperature, 'f', 1);
    row["voltage"] = QString::number(voltage, 'f', 1);
    row["current"] = QString::number(current, 'f', 3);
    row["switch1"] = QString::number(switch1);

    m_smokeTableData.prepend(row);

    // 保持最多10行数据
    if (m_smokeTableData.size() > MAX_TABLE_ROWS) {
        m_smokeTableData.removeLast();
    }

    debug_printf("表格数据已添加: 时间=%s, O2=%.3f%%, CO=%.0fppm, 总行数=%d\n",
                row["time"].toString().toStdString().c_str(),
                o2, co, m_smokeTableData.size());

    emit smokeTableDataChanged();
}


void MonitoringDataSource::updateSmokeChartSeries(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int zoomIndex)
{
    debug_printf("图表更新: updateSmokeChartSeries被调用, zoomIndex=%d, O2数据点数=%d\n", zoomIndex, m_smokeO2Data.size());

    if (!o2Series || !coSeries) {
        debug_printf("图表更新: 错误 - 图表系列指针为空 (o2=%p, co=%p)\n",
                    o2Series, coSeries);
        return;
    }

    if (m_smokeO2Data.isEmpty()) {
        debug_printf("图表更新: 警告 - 没有数据可显示\n");
        return;
    }

    QtCharts::QLineSeries *o2LineSeries = qobject_cast<QtCharts::QLineSeries*>(o2Series);
    QtCharts::QLineSeries *coLineSeries = qobject_cast<QtCharts::QLineSeries*>(coSeries);

    if (!o2LineSeries || !coLineSeries) {
        return;
    }

    // 清空现有数据
    o2LineSeries->clear();
    coLineSeries->clear();

    // 根据缩放级别确定显示的时间范围
    double displayRangeHours = 24.0;  // 默认24小时
    switch(zoomIndex) {
        case 0: displayRangeHours = 24.0; break;  // 24小时
        case 1: displayRangeHours = 12.0; break;  // 12小时
        case 2: displayRangeHours = 8.0; break;   // 8小时
        case 3: displayRangeHours = 1.0; break;   // 1小时（这里用小时计算，后面会转换）
        default: displayRangeHours = 24.0; break;
    }

    // 根据缩放级别确定抽样间隔和最大点数 - QtCharts性能优化
    int samplingInterval = 1;  // 抽样间隔
    int maxPoints = 1000;      // 最大绘制点数

    switch(zoomIndex) {
        case 0: // 24小时视图
            samplingInterval = 20;  // 每20个点取1个（3秒×20=1分钟间隔）
            maxPoints = 1440;       // 最多1440个点（24小时×60分钟）
            break;
        case 1: // 12小时视图
            samplingInterval = 10;  // 每10个点取1个（3秒×10=30秒间隔）
            maxPoints = 1440;       // 最多1440个点（12小时×120个30秒）
            break;
        case 2: // 8小时视图
            samplingInterval = 5;   // 每5个点取1个（3秒×5=15秒间隔）
            maxPoints = 1920;       // 最多1920个点（8小时×240个15秒）
            break;
        case 3: // 1小时视图
            samplingInterval = 1;   // 保持原精度（3秒间隔）
            maxPoints = 1200;       // 最多1200个点（1小时×1200个3秒）
            break;
    }

    debug_printf("图表性能优化: 抽样间隔=%d, 最大点数=%d, 原始数据点数=%d\n",
                samplingInterval, maxPoints, m_smokeO2Data.size());

    // 计算时间偏移量：当数据超过显示范围时，让曲线向左移动
    double timeOffset = 0.0;
    if (!m_smokeO2Data.isEmpty()) {
        QVariantMap lastPoint = m_smokeO2Data.last().toMap();
        double currentTime = lastPoint["x"].toReal();

        // 如果当前时间超过显示范围，计算偏移量让最新数据显示在范围末尾
        if (currentTime > displayRangeHours) {
            timeOffset = currentTime - displayRangeHours;
        }
    }

    // 添加O2数据（使用抽样优化）
    int addedO2Points = 0;
    for (int i = 0; i < m_smokeO2Data.size() && addedO2Points < maxPoints; i += samplingInterval) {
        QVariantMap point = m_smokeO2Data[i].toMap();
        qreal absoluteTime = point["x"].toReal();
        qreal displayTime = absoluteTime - timeOffset;  // 减去偏移量
        qreal value = point["y"].toReal();

        // 只显示在显示范围内的数据点
        if (displayTime >= 0.0 && displayTime <= displayRangeHours) {
            o2LineSeries->append(displayTime, value);
            addedO2Points++;
        }
    }

    // 添加CO数据（使用抽样优化）
    int addedCOPoints = 0;
    for (int i = 0; i < m_smokeCOData.size() && addedCOPoints < maxPoints; i += samplingInterval) {
        QVariantMap point = m_smokeCOData[i].toMap();
        qreal absoluteTime = point["x"].toReal();
        qreal displayTime = absoluteTime - timeOffset;
        qreal value = point["y"].toReal();

        if (displayTime >= 0.0 && displayTime <= displayRangeHours) {
            coLineSeries->append(displayTime, value);
            addedCOPoints++;
        }
    }

    debug_printf("图表更新完成: O2系列点数=%d (抽样后), CO系列点数=%d (抽样后), 性能提升=%.1f%%\n",
                o2LineSeries->count(), coLineSeries->count(),
                (1.0 - (double)o2LineSeries->count() / m_smokeO2Data.size()) * 100.0);
}

void MonitoringDataSource::updateSmokeChartSeriesWithMinutes(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries)
{
    debug_printf("图表更新(分钟视图): updateSmokeChartSeriesWithMinutes被调用, O2数据点数=%d\n", m_smokeO2Data.size());

    if (!o2Series || !coSeries) {
        return;
    }

    QtCharts::QLineSeries *o2LineSeries = qobject_cast<QtCharts::QLineSeries*>(o2Series);
    QtCharts::QLineSeries *coLineSeries = qobject_cast<QtCharts::QLineSeries*>(coSeries);

    if (!o2LineSeries || !coLineSeries) {
        return;
    }

    // 清空现有数据
    o2LineSeries->clear();
    coLineSeries->clear();

    // 1小时分钟视图的性能优化参数
    int samplingInterval = 1;   // 保持3秒精度，不抽样
    int maxPoints = 1200;       // 最多1200个点（1小时×1200个3秒）

    debug_printf("图表性能优化(分钟视图): 抽样间隔=%d, 最大点数=%d, 原始数据点数=%d\n",
                samplingInterval, maxPoints, m_smokeO2Data.size());

    // 计算分钟时间偏移量：当数据超过60分钟时，让曲线向左移动
    double timeOffsetMinutes = 0.0;
    if (!m_smokeO2Data.isEmpty()) {
        QVariantMap lastPoint = m_smokeO2Data.last().toMap();
        double currentTimeMinutes = lastPoint["x_minutes"].toReal();

        // 如果当前时间超过60分钟，计算偏移量让最新数据显示在60分钟位置
        if (currentTimeMinutes > 60.0) {
            timeOffsetMinutes = currentTimeMinutes - 60.0;
        }
    }

    // 添加O2数据（X轴固定0-60分钟，使用性能优化）
    int addedO2Points = 0;
    for (int i = 0; i < m_smokeO2Data.size() && addedO2Points < maxPoints; i += samplingInterval) {
        QVariantMap point = m_smokeO2Data[i].toMap();
        qreal absoluteTimeMinutes = point["x_minutes"].toReal();
        qreal displayTimeMinutes = absoluteTimeMinutes - timeOffsetMinutes;
        qreal value = point["y"].toReal();

        // 只显示在0-60范围内的数据点
        if (displayTimeMinutes >= 0.0 && displayTimeMinutes <= 60.0) {
            o2LineSeries->append(displayTimeMinutes, value);
            addedO2Points++;
        }
    }

    // 添加CO数据（使用性能优化）
    int addedCOPoints = 0;
    for (int i = 0; i < m_smokeCOData.size() && addedCOPoints < maxPoints; i += samplingInterval) {
        QVariantMap point = m_smokeCOData[i].toMap();
        qreal absoluteTimeMinutes = point["x_minutes"].toReal();
        qreal displayTimeMinutes = absoluteTimeMinutes - timeOffsetMinutes;
        qreal value = point["y"].toReal();

        if (displayTimeMinutes >= 0.0 && displayTimeMinutes <= 60.0) {
            coLineSeries->append(displayTimeMinutes, value);
            addedCOPoints++;
        }
    }

    debug_printf("图表更新完成(分钟视图): O2系列点数=%d (优化后), CO系列点数=%d (优化后)\n",
                o2LineSeries->count(), coLineSeries->count());
}

void MonitoringDataSource::updateSmokeChartSeriesWithScroll(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int zoomIndex, double scrollOffset)
{
    if (!o2Series || !coSeries) {
        return;
    }

    QtCharts::QLineSeries *o2LineSeries = qobject_cast<QtCharts::QLineSeries*>(o2Series);
    QtCharts::QLineSeries *coLineSeries = qobject_cast<QtCharts::QLineSeries*>(coSeries);

    if (!o2LineSeries || !coLineSeries) {
        return;
    }

    // 清空现有数据
    o2LineSeries->clear();
    coLineSeries->clear();

    // 根据缩放级别确定显示的时间范围
    double displayRangeHours = 24.0;  // 默认24小时
    switch(zoomIndex) {
        case 0: displayRangeHours = 24.0; break;  // 24小时
        case 1: displayRangeHours = 12.0; break;  // 12小时
        case 2: displayRangeHours = 8.0; break;   // 8小时
        case 3: displayRangeHours = 1.0; break;   // 1小时
        default: displayRangeHours = 24.0; break;
    }

    // 使用手动滚动偏移量而不是自动计算
    double timeOffset = scrollOffset;

    // 添加O2数据（X轴固定，曲线根据timeOffset移动）
    for (int i = 0; i < m_smokeO2Data.size(); ++i) {
        QVariantMap point = m_smokeO2Data[i].toMap();
        qreal absoluteTime = point["x"].toReal();
        qreal displayTime = absoluteTime - timeOffset;  // 减去偏移量
        qreal value = point["y"].toReal();

        // 只显示在显示范围内的数据点
        if (displayTime >= 0.0 && displayTime <= displayRangeHours) {
            o2LineSeries->append(displayTime, value);
        }
    }

    // 添加CO数据
    for (int i = 0; i < m_smokeCOData.size(); ++i) {
        QVariantMap point = m_smokeCOData[i].toMap();
        qreal absoluteTime = point["x"].toReal();
        qreal displayTime = absoluteTime - timeOffset;
        qreal value = point["y"].toReal();

        if (displayTime >= 0.0 && displayTime <= displayRangeHours) {
            coLineSeries->append(displayTime, value);
        }
    }
}

void MonitoringDataSource::reinitializeSerialConnection(const QString &oldBoiler, const QString &newBoiler)
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;

    if (newBoiler.isEmpty()) {
        debug_printf("新烟气分析仪名称为空，跳过串口重新初始化\n");
        return;
    }

    std::string newDeviceName = newBoiler.toStdString();
    debug_printf("开始为烟气分析仪 '%s' 重新初始化串口连接\n", newDeviceName.c_str());

    // 查找新烟气分析仪
    auto newIt = boiler_map.find(newDeviceName);
    if (newIt == boiler_map.end() || newIt->second == nullptr) {
        debug_printf("错误: 找不到烟气分析仪 '%s'，设备映射大小: %zu\n", newDeviceName.c_str(), boiler_map.size());
        return;
    }

    Boiler* newDeviceObj = newIt->second;
    debug_printf("找到烟气分析仪 '%s'，当前fd: %d，协议: '%s'\n",
                newDeviceName.c_str(), newDeviceObj->fd, newDeviceObj->protocol.c_str());

    // 检查新烟气分析仪的串口连接状态
    if (newDeviceObj->fd < 0) {
        // 串口未连接，需要重新初始化
        debug_printf("烟气分析仪 '%s' 串口未连接，开始重新初始化串口连接\n", newDeviceName.c_str());

        // 获取配置管理器
        extern ConfigManager* g_config_manager;
        if (g_config_manager == nullptr) {
            debug_printf("错误: 全局配置管理器未初始化\n");
            return;
        }

        try {
            // 重新获取协议配置并初始化串口
            std::string protocol = newDeviceObj->protocol;
            if (protocol.empty()) {
                // 如果协议为空，从配置文件重新读取
                protocol = g_config_manager->get<std::string>(newDeviceName, "Protocol");
                newDeviceObj->protocol = protocol;
                debug_printf("从配置文件重新读取烟气分析仪 '%s' 的协议: '%s'\n", newDeviceName.c_str(), protocol.c_str());
            }

            // 获取协议对应的串口配置
            std::string port = g_config_manager->get<std::string>(protocol, "Port");
            int baud_rate = g_config_manager->get<int>(protocol, "BaudRate", 9600);
            char parity = g_config_manager->get<char>(protocol, "Parity");
            int stop_bits = g_config_manager->get<int>(protocol, "StopBits", 1);
            int data_bits = g_config_manager->get<int>(protocol, "DataBits", 8);

            debug_printf("重新初始化烟气分析仪 '%s' 串口配置:\n", newDeviceName.c_str());
            debug_printf("  协议: '%s'\n", protocol.c_str());
            debug_printf("  端口: '%s'\n", port.c_str());
            debug_printf("  波特率: %d\n", baud_rate);
            debug_printf("  校验位: %c\n", parity);
            debug_printf("  停止位: %d\n", stop_bits);
            debug_printf("  数据位: %d\n", data_bits);

            // 重新打开串口
            extern int open_serial_port(const char *device, int speed, char parity, int stop_bits, int data_bits);
            int new_fd = open_serial_port(port.c_str(), baud_rate, parity, stop_bits, data_bits);

            if (new_fd >= 0) {
                newDeviceObj->fd = new_fd;
                debug_printf("烟气分析仪 '%s' 串口重新初始化成功，文件描述符: %d\n", newDeviceName.c_str(), new_fd);
            } else {
                debug_printf("烟气分析仪 '%s' 串口重新初始化失败\n", newDeviceName.c_str());
            }
        } catch (const std::exception& e) {
            debug_printf("重新初始化烟气分析仪 '%s' 串口时发生异常: %s\n", newDeviceName.c_str(), e.what());
        } catch (...) {
            debug_printf("重新初始化烟气分析仪 '%s' 串口时发生未知异常\n", newDeviceName.c_str());
        }
    } else {
        debug_printf("烟气分析仪 '%s' 串口已连接，文件描述符: %d\n", newDeviceName.c_str(), newDeviceObj->fd);
    }

    // 输出切换完成信息
    if (!oldBoiler.isEmpty()) {
        debug_printf("烟气分析仪切换完成: 从 '%s' 切换到 '%s'\n", oldBoiler.toStdString().c_str(), newDeviceName.c_str());
    } else {
        debug_printf("初始化烟气分析仪 '%s' 完成\n", newDeviceName.c_str());
    }

#else
    debug_printf("硬件数据采集被禁用，跳过串口重新初始化\n");
#endif
}

void MonitoringDataSource::updateTimerInterval()
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;
    extern ConfigManager* g_config_manager;

    if (g_config_manager == nullptr) {
        debug_printf("⚠ 配置管理器未初始化，无法设置UI更新间隔\n");
        return;
    }

    // 如果没有选择锅炉，尝试从第一个可用锅炉获取配置
    std::string boilerName;
    if (m_currentBoiler.isEmpty()) {
        if (!boiler_map.empty()) {
            boilerName = boiler_map.begin()->first;
            debug_printf("调试: 没有选择锅炉，使用第一个可用锅炉 '%s' 的配置\n", boilerName.c_str());
        } else {
            debug_printf("⚠ 没有可用的锅炉配置\n");
            return;
        }
    } else {
        boilerName = m_currentBoiler.toStdString();
    }

    // 首先尝试从锅炉对象获取采集间隔
    auto it = boiler_map.find(boilerName);
    if (it != boiler_map.end() && it->second != nullptr && it->second->is_initialized) {
        int collectionInterval = it->second->collection_interval;
        int uiInterval = collectionInterval * 1000; // 转换为毫秒，与采集间隔保持一致
        m_timer->setInterval(uiInterval);
        debug_printf("设置UI更新间隔: %d毫秒 (采集间隔: %d秒)\n", uiInterval, collectionInterval);
        return;
    }

    // 如果锅炉对象不可用，直接从配置文件读取
    if (g_config_manager->exists(boilerName, "CollectionInterval")) {
        int collectionInterval = g_config_manager->get<int>(boilerName, "CollectionInterval");
        if (collectionInterval > 0) {
            int uiInterval = collectionInterval * 1000; // 转换为毫秒，与采集间隔保持一致
            m_timer->setInterval(uiInterval);
            debug_printf("设置UI更新间隔: %d毫秒 (采集间隔: %d秒)\n", uiInterval, collectionInterval);
            return;
        }
    }
#endif
}

void MonitoringDataSource::updateChartIncremental(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int zoomIndex)
{
    // 减少调试输出以提升性能
    static int updateCount = 0;
    updateCount++;

    if (updateCount % 10 == 0) {  // 每10次更新才输出一次日志
        debug_printf("渐进式图表更新: 第%d次调用, zoomIndex=%d, O2数据点数=%d\n", updateCount, zoomIndex, m_smokeO2Data.size());
    }

    if (!o2Series || !coSeries || m_smokeO2Data.isEmpty()) {
        return;
    }

    QtCharts::QLineSeries *o2LineSeries = qobject_cast<QtCharts::QLineSeries*>(o2Series);
    QtCharts::QLineSeries *coLineSeries = qobject_cast<QtCharts::QLineSeries*>(coSeries);

    if (!o2LineSeries || !coLineSeries) {
        return;
    }

    // 根据缩放级别确定最大显示点数（恢复合理数量保证曲线平滑）
    int maxDisplayPoints = 600;  // 恢复合理的默认点数
    switch(zoomIndex) {
        case 0: maxDisplayPoints = 600; break;  // 24小时：600点
        case 1: maxDisplayPoints = 500; break;  // 12小时：500点
        case 2: maxDisplayPoints = 400; break;  // 8小时：400点
        case 3: maxDisplayPoints = 300; break;  // 1小时：300点
        default: maxDisplayPoints = 600; break;
    }

    // 如果图表为空，进行全量更新
    if (o2LineSeries->count() == 0) {
        updateSmokeChartSeries(o2Series, coSeries, zoomIndex);
        return;
    }

    // 简化的增量更新：只添加最新的1-2个数据点
    int dataSize = m_smokeO2Data.size();
    int startIndex = qMax(0, dataSize - 2);  // 只取最新的2个点

    for (int i = startIndex; i < dataSize; ++i) {
        QVariantMap o2Point = m_smokeO2Data[i].toMap();
        QVariantMap coPoint = m_smokeCOData[i].toMap();

        double timeValue = (zoomIndex == 3) ? o2Point["x_minutes"].toReal() : o2Point["x"].toReal();

        appendNewChartPoint(o2Series, timeValue, o2Point["y"].toReal(), maxDisplayPoints);
        appendNewChartPoint(coSeries, timeValue, coPoint["y"].toReal(), maxDisplayPoints);
    }

    if (updateCount % 10 == 0) {
        debug_printf("渐进式图表更新完成: O2系列点数=%d, CO系列点数=%d\n",
                    o2LineSeries->count(), coLineSeries->count());
    }
}

void MonitoringDataSource::appendNewChartPoint(QtCharts::QAbstractSeries *series, double x, double y, int maxPoints)
{
    QtCharts::QLineSeries *lineSeries = qobject_cast<QtCharts::QLineSeries*>(series);
    if (!lineSeries) {
        return;
    }

    // 如果达到最大点数，移除最旧的点
    if (lineSeries->count() >= maxPoints) {
        lineSeries->removePoints(0, 1);
    }

    // 添加新点
    lineSeries->append(x, y);
}
